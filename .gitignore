# Dependencies
node_modules/
.pnp/
.pnp.js

# Build outputs
dist/
build/
out/
.next/
.nuxt/
.output/

# Database files
*.db
*.db-journal
*.sqlite
*.sqlite3
data/
data/*.db
data/*.json

# Docker related
.docker/
docker-compose.override.yml

# Environment variables
.env
.env.*
!.env.example

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Lock files
package-lock.json
yarn.lock
pnpm-lock.yaml
bun.lockb

# Editor directories and files
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.DS_Store
Thumbs.db

# Testing
coverage/
.nyc_output/

# Temporary files
.tmp/
.temp/
.cache/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
