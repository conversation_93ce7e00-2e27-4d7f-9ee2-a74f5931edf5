FROM python:3.12-slim

WORKDIR /app/data

RUN apt-get update && \
    apt-get upgrade -y && \
    apt-get install -y \
    curl \
    wget \
    git \
    build-essential \
    nano \
    iputils-ping \
    net-tools \
    && apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Install pip and common packages
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir \
    setuptools \
    wheel \
    virtualenv \
    flask \
    requests \
    numpy \
    pandas

CMD sh -c "$START"
