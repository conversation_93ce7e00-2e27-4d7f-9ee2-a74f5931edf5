#!/bin/bash

# Exit on error
set -e

# GitHub Container Registry
REGISTRY="ghcr.io/airlinklabs"

# Function to build and push multi-architecture images
build_push_image() {
    local IMAGE_NAME=$1
    local DOCKERFILE_PATH=$2
    local TAG=$3
    
    echo "Building and pushing $IMAGE_NAME:$TAG..."
    
    # Create builder if it doesn't exist
    if ! docker buildx inspect multiarch-builder &>/dev/null; then
        docker buildx create --name multiarch-builder --driver docker-container --use
    else
        docker buildx use multiarch-builder
    fi
    
    # Build and push for both architectures
    docker buildx build \
        --platform linux/amd64,linux/arm64 \
        -t $REGISTRY/$IMAGE_NAME:$TAG \
        -f $DOCKERFILE_PATH \
        --push \
        .
        
    echo "$IMAGE_NAME:$TAG built and pushed successfully"
}

# Build and push Python image
if [ -d "python" ]; then
    build_push_image "python" "python/Dockerfile" "3.12"
fi

# Build and push Java images
for version in 11 16 17 18 21; do
    if [ -d "java/java:$version" ]; then
        build_push_image "java" "java/java:$version/Dockerfile" "$version"
    fi
done

# Build and push Node image
if [ -d "node:21" ]; then
    build_push_image "node" "node:21/Dockerfile" "21"
fi

# Build and push Debian image
if [ -d "debian" ]; then
    build_push_image "debian" "debian/Dockerfile" "bookworm-slim"
fi

# Build and push installers
if [ -d "installers/debian" ]; then
    build_push_image "installers" "installers/debian/Dockerfile" "debian"
fi

echo "All images built and pushed successfully"
