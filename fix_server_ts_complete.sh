#!/bin/bash

# Fix include statements
sed -i '' 's/include: { Node: true, image: true }/include: { Node: true, Images: true }/g' src/modules/user/server.ts
sed -i '' 's/include: { Node: true, image: true, owner: true }/include: { Node: true, Images: true, Users: true }/g' src/modules/user/server.ts
sed -i '' 's/include: { image: true, node: true }/include: { Images: true, Node: true }/g' src/modules/user/server.ts

# Fix image references
sed -i '' 's/server\.image && typeof server\.Images\.info/server\.Images && typeof server\.Images\.info/g' src/modules/user/server.ts
sed -i '' 's/server\.image && server\.Images\.dockerImages/server\.Images && server\.Images\.dockerImages/g' src/modules/user/server.ts
sed -i '' 's/serverToReinstall\.image/serverToReinstall\.Images/g' src/modules/user/server.ts

# Fix remaining image references
sed -i '' 's/server\.image &&/server\.Images &&/g' src/modules/user/server.ts

echo "Fixed server.ts file"
