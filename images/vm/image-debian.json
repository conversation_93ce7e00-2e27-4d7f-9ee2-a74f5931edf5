{"meta": {"version": "AL_V1"}, "name": "Debian VM Image", "description": "Debian is a popular, stable, and versatile Linux distribution. It is known for its stability, extensive package repository, and active community. Debian is often used as a base for other distributions and is the preferred choice for servers and desktop systems alike, known for its reliability and long-term support.", "author": "<EMAIL>", "authorName": "AirLink Labs", "docker_images": [{"Debian Bookworm-slim": "ghcr.io/airlinklabs/debian:bookworm-slim"}], "startup": "bash", "info": {"features": ["vm", "alsh"], "stop": "shutdown -h now"}, "scripts": {"install": [], "native": {"CMD": "git clone https://github.com/airlinklabs/alsh.git /app/data/alsh && cd /app/data/alsh && npm i && mkdir -p /app/data/airlink && openssl rand -base64 12 > /app/data/airlink/password.txt && sleep 30", "container": "ghcr.io/airlinklabs/installers:debian"}}, "variables": [{"env": "ALSH", "value": "true", "type": "boolean", "name": "ALSH terminal streaming", "required": true}]}