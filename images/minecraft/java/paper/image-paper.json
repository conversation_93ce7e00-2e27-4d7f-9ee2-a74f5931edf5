{"meta": {"version": "AL_V1"}, "name": "Minecraft Java: Paper", "description": "PaperMC is an optimized, high-performance fork of SpigotMC for Minecraft servers. It aims to improve performance and stability by offering additional features, enhanced configuration options, and bug fixes. Known for its robust plugin compatibility and active development community, PaperMC is a preferred choice for server administrators looking to enhance their Minecraft multiplayer experience.", "author": "<EMAIL>", "authorName": "AirLink Labs", "docker_images": [{"Java 21": "ghcr.io/airlinklabs/java:21"}, {"Java 18": "ghcr.io/airlinklabs/java:18"}, {"Java 17": "ghcr.io/airlinklabs/java:17"}, {"Java 16": "ghcr.io/airlinklabs/java:16"}, {"Java 11": "ghcr.io/airlinklabs/java:11"}], "startup": "java -Xms128M -XX:MaxRAMPercentage=95.0 -Dterminal.jline=false -Dterminal.ansi=true -jar $ALVKT(JAR_STARTUP)", "info": {"features": ["eula", "auto-complete", "players", "worlds"], "stop": "stop", "startup_line": {"regex": "^Done \\(\\d+\\.\\d{3}s\\)! For help, type \"help\"$", "with": "\u001b[33mDone server started successfully in \u001b[37m(\\d+\\.\\d{3}s)\u001b[33m! For help, type help\u001b[0m"}}, "scripts": {"install": [{"url": "https://api.papermc.io/v2/projects/paper/versions/$ALVKT(VERSION)/builds/$ALVKT(BUILD)/downloads/paper-$ALVKT(VERSION)-$ALVKT(BUILD).jar", "onStart": false, "ALVKT": false, "fileName": "server.jar"}, {"url": "https://raw.githubusercontent.com/airlinklabs/images/refs/heads/main/minecraft/java/server.properties", "onStart": true, "ALVKT": true, "fileName": "server.properties"}]}, "variables": [{"env": "JAR_STARTUP", "value": "server.jar", "type": "text", "name": "<PERSON><PERSON>", "required": true}, {"env": "VERSION", "value": "1.21.1", "type": "text", "name": "Minecraft Version", "required": true}, {"env": "BUILD", "value": "89", "type": "number", "name": "Build Number", "required": true}]}