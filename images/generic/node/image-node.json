{"meta": {"version": "AL_V1"}, "name": "Generic: Node JS", "description": "This is a generic node.js server image.", "author": "<EMAIL>", "authorName": "AirLink Labs", "docker_images": [{"Node 21": "ghcr.io/airlinklabs/node:21"}], "startup": "bash", "info": {"features": []}, "scripts": {"install": [], "native": {"CMD": "echo 'console.log('Hello World');' > /app/data/index.js && cd /app/data && node index.js", "container": "ghcr.io/airlinklabs/installers:debian"}}, "variables": []}