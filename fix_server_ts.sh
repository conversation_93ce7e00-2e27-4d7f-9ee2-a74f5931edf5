#!/bin/bash

# Fix node references
sed -i '' 's/include: { node: true/include: { Node: true/g' panel/src/modules/user/server.ts
sed -i '' 's/include: { node: true, image: true/include: { Node: true, Images: true/g' panel/src/modules/user/server.ts
sed -i '' 's/include: { node: true, image: true, owner: true/include: { Node: true, Images: true, Users: true/g' panel/src/modules/user/server.ts
sed -i '' 's/server\.node\./server\.Node\./g' panel/src/modules/user/server.ts

# Fix image references
sed -i '' 's/server\.image\./server\.Images\./g' panel/src/modules/user/server.ts

echo "Fixed server.ts file"
