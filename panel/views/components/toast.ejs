<script>
    (function() {
      if (window.createToastSystem) return;

      window.createToastSystem = () => {
        let toastContainer = document.getElementById('toast-container');
        if (!toastContainer) {
          toastContainer = document.createElement('div');
          toastContainer.id = 'toast-container';
          toastContainer.className = 'fixed top-4 right-4 z-50 flex flex-col gap-2';
          document.body.appendChild(toastContainer);
        }

        const showToast = (message, type = 'error') => {
          const toast = document.createElement('div');
          toast.className = `
            px-4 py-3 rounded-xl shadow-lg transform transition-all duration-300 ease-in-out
            opacity-0 translate-x-full flex items-center gap-3 border
          `;

          if (type === 'error') {
            toast.classList.add('bg-white', 'dark:bg-neutral-800', 'text-red-600', 'dark:text-red-400', 'border-red-200', 'dark:border-red-500/20');
          } else if (type === 'success') {
            toast.classList.add('bg-white', 'dark:bg-neutral-800', 'text-emerald-600', 'dark:text-emerald-400', 'border-emerald-200', 'dark:border-emerald-500/20');
          } else if (type === 'warning') {
            toast.classList.add('bg-white', 'dark:bg-neutral-800', 'text-amber-600', 'dark:text-amber-400', 'border-amber-200', 'dark:border-amber-500/20');
          } else {
            toast.classList.add('bg-white', 'dark:bg-neutral-800', 'text-blue-600', 'dark:text-blue-400', 'border-blue-200', 'dark:border-blue-500/20');
          }

          const icon = document.createElement('span');
          if (type === 'error') {
            icon.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5 text-red-500">
              <path fill-rule="evenodd" d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25zm-1.72 6.97a.75.75 0 10-1.06 1.06L10.94 12l-1.72 1.72a.75.75 0 101.06 1.06L12 13.06l1.72 1.72a.75.75 0 101.06-1.06L13.06 12l1.72-1.72a.75.75 0 10-1.06-1.06L12 10.94l-1.72-1.72z" clip-rule="evenodd" />
            </svg>`;
          } else if (type === 'success') {
            icon.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5 text-emerald-500">
              <path fill-rule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm13.36-1.814a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z" clip-rule="evenodd" />
            </svg>`;
          } else if (type === 'warning') {
            icon.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5 text-amber-500">
              <path fill-rule="evenodd" d="M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z" clip-rule="evenodd" />
            </svg>`;
          } else {
            icon.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5 text-blue-500">
              <path fill-rule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm8.706-1.442c1.146-.573 2.437.463 2.126 1.706l-.709 2.836.042-.02a.75.75 0 01.67 1.34l-.04.022c-1.147.573-2.438-.463-2.127-1.706l.71-2.836-.042.02a.75.75 0 11-.671-1.34l.041-.022zM12 9a.75.75 0 100-********* 0 000 1.5z" clip-rule="evenodd" />
            </svg>`;
          }

          toast.appendChild(icon);

          const messageSpan = document.createElement('span');
          messageSpan.className = 'font-medium text-sm';
          messageSpan.textContent = message;
          toast.appendChild(messageSpan);
          toastContainer.appendChild(toast);

          requestAnimationFrame(() => {
            toast.classList.remove('opacity-0', 'translate-x-full');
            toast.classList.add('opacity-100', 'translate-x-0');
          });

          setTimeout(() => {
            toast.classList.add('opacity-0', 'translate-x-full');
            setTimeout(() => {
              toastContainer.removeChild(toast);
            }, 300);
          }, 5000);
        };

        return { showToast };
      };

      const { showToast } = window.createToastSystem();
      window.showToast = showToast;
    })();
  </script>
