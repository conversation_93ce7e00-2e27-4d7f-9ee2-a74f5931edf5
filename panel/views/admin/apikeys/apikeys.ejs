<%- include('../../components/header', { title: 'API Keys Management' }) %>

<style>
  @keyframes fadeIn {
    from { opacity: 0; transform: scale(0.95); }
    to { opacity: 1; transform: scale(1); }
  }

  .animate-fadeIn {
    animation: fadeIn 0.2s ease-out;
  }

  .permission-category {
    transition: all 0.2s ease-out;
  }

  .permission-category:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }
</style>

<main class="h-screen m-auto">
  <div class="flex h-screen">

    <div class="w-60 h-full">
      <%- include('../../components/template') %>
    </div>

    <div class="flex-1 p-6 overflow-y-auto pt-16">
      <div class="sm:flex sm:items-center px-8 pt-4">
        <div class="sm:flex-auto">
          <h1 class="text-base font-medium leading-6 text-neutral-800 dark:text-white">API Keys Management</h1>
          <p class="mt-1 tracking-tight text-sm text-neutral-500">Create and manage API keys with specific permissions</p>
        </div>
        <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
          <div class="flex gap-2">
            <button id="createApiKeyBtn" type="button" class="w-full md:w-auto rounded-xl bg-neutral-950 dark:bg-white hover:bg-neutral-300 text-neutral-200 dark:text-neutral-800 px-3 py-2 text-sm font-medium shadow-md transition focus:outline focus:outline-2 focus:outline-offset-2">
              Create API Key
            </button>
            <a href="/admin/api/docs" class="w-full md:w-auto rounded-xl border border-neutral-300 dark:border-neutral-700 bg-white dark:bg-neutral-800 hover:bg-neutral-50 dark:hover:bg-neutral-700 text-neutral-700 dark:text-neutral-300 px-3 py-2 text-sm font-medium shadow-sm transition focus:outline focus:outline-2 focus:outline-offset-2">
              API Documentation
            </a>
          </div>
        </div>
      </div>
      <div class="px-8 mt-5">
        <div class="rounded-xl bg-neutral-700/10 dark:bg-neutral-900 p-6">
          <div class="sm:flex sm:items-center">
            <div class="sm:flex-auto">
              <h1 class="text-base font-semibold leading-6 text-neutral-800 dark:text-white">API Keys</h1>
              <p class="mt-2 text-sm text-neutral-500">A list of all API keys in your panel.</p>
            </div>
          </div>
          <div class="mt-8 flow-root">
            <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
              <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                <table class="min-w-full divide-y divide-neutral-700/10 dark:divide-neutral-800">
                  <thead>
                    <tr>
                      <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-neutral-800 dark:text-white sm:pl-0">Name</th>
                      <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-neutral-800 dark:text-white">Key</th>
                      <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-neutral-800 dark:text-white">Created By</th>
                      <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-neutral-800 dark:text-white">Status</th>
                      <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-neutral-800 dark:text-white">Created</th>
                      <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-0">
                        <span class="sr-only">Actions</span>
                      </th>
                    </tr>
                  </thead>
                  <tbody class="divide-y divide-neutral-700/10 dark:divide-neutral-800">
                    <% if (apiKeys.length === 0) { %>
                      <tr>
                        <td colspan="6" class="py-4 pl-4 pr-3 text-sm text-center text-neutral-500 sm:pl-0">No API keys found</td>
                      </tr>
                    <% } else { %>
                      <% apiKeys.forEach(apiKey => { %>
                        <tr>
                          <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-neutral-800 dark:text-white sm:pl-0">
                            <%= apiKey.name %>
                            <% if (apiKey.description) { %>
                              <p class="text-xs text-neutral-500"><%= apiKey.description %></p>
                            <% } %>
                          </td>
                          <td class="whitespace-nowrap px-3 py-4 text-sm text-neutral-500">
                            <div class="flex items-center">
                              <span class="font-mono truncate max-w-[150px]"><%= apiKey.key.substring(0, 8) %>...</span>
                              <button
                                class="ml-2 text-neutral-400 hover:text-neutral-600 dark:hover:text-neutral-300"
                                onclick="copyToClipboard('<%= apiKey.key %>')"
                                title="Copy API key"
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                                  <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 01-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 011.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 00-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 01-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 00-3.375-3.375h-1.5a1.125 1.125 0 01-1.125-1.125v-1.5a3.375 3.375 0 00-3.375-3.375H9.75" />
                                </svg>
                              </button>
                            </div>
                          </td>
                          <td class="whitespace-nowrap px-3 py-4 text-sm text-neutral-500">
                            <%= apiKey.user ? apiKey.user.username : 'System' %>
                          </td>
                          <td class="whitespace-nowrap px-3 py-4 text-sm">
                            <% if (apiKey.active) { %>
                              <span class="inline-flex items-center rounded-md bg-green-50 dark:bg-green-900/20 px-2 py-1 text-xs font-medium text-green-700 dark:text-green-400 ring-1 ring-inset ring-green-600/20 dark:ring-green-500/30">Active</span>
                            <% } else { %>
                              <span class="inline-flex items-center rounded-md bg-red-50 dark:bg-red-900/20 px-2 py-1 text-xs font-medium text-red-700 dark:text-red-400 ring-1 ring-inset ring-red-600/20 dark:ring-red-500/30">Inactive</span>
                            <% } %>
                          </td>
                          <td class="whitespace-nowrap px-3 py-4 text-sm text-neutral-500">
                            <%= new Date(apiKey.createdAt).toLocaleDateString() %>
                          </td>
                          <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-0">
                            <div class="flex gap-2 justify-end">
                              <button
                                onclick="showEditModal('<%= apiKey.id %>', '<%= apiKey.name %>', '<%= apiKey.description || '' %>', <%= apiKey.permissions %>)"
                                class="text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-neutral-200"
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                                  <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21h-9.5A2.25 2.25 0 014 18.75V8.25A2.25 2.25 0 016.25 6H11" />
                                </svg>
                              </button>
                              <form action="/admin/apikeys/toggle/<%= apiKey.id %>" method="POST" class="inline">
                                <button type="submit" class="text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-neutral-200">
                                  <% if (apiKey.active) { %>
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                                      <path stroke-linecap="round" stroke-linejoin="round" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636" />
                                    </svg>
                                  <% } else { %>
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                                      <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                  <% } %>
                                </button>
                              </form>
                              <form action="/admin/apikeys/delete/<%= apiKey.id %>" method="POST" class="inline" onsubmit="return confirm('Are you sure you want to delete this API key?')">
                                <button type="submit" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0" />
                                  </svg>
                                </button>
                              </form>
                            </div>
                          </td>
                        </tr>
                      <% }); %>
                    <% } %>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</main>


<div id="createApiKeyModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
  <div class="flex min-h-full items-center justify-center p-4 text-center">
    <div class="relative transform overflow-hidden rounded-xl bg-white dark:bg-neutral-800 px-5 py-5 text-left shadow-2xl transition-all w-full max-w-3xl border border-neutral-200 dark:border-neutral-700 animate-fadeIn">


      <div class="flex justify-between items-center border-b border-neutral-200 dark:border-neutral-700 pb-3 mb-3">
        <h3 class="text-lg font-semibold text-neutral-900 dark:text-white">Create New API Key</h3>
        <button type="button" onclick="hideCreateModal()" class="text-neutral-400 hover:text-neutral-500 focus:outline-none">
          <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <form action="/admin/apikeys/create" method="POST" class="space-y-4">


        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label for="name" class="block text-sm font-medium text-neutral-900 dark:text-white">Name</label>
            <input type="text" name="name" id="name" required class="mt-1 block w-full rounded-xl border-neutral-300 dark:border-neutral-700 bg-white dark:bg-neutral-800 text-neutral-900 dark:text-white shadow-sm focus:border-neutral-500 focus:ring-neutral-500 sm:text-sm" placeholder="My API Key">
          </div>
          <div>
            <label for="description" class="block text-sm font-medium text-neutral-900 dark:text-white">Description</label>
            <textarea name="description" id="description" rows="1" class="mt-1 block w-full rounded-xl border-neutral-300 dark:border-neutral-700 bg-white dark:bg-neutral-800 text-neutral-900 dark:text-white shadow-sm focus:border-neutral-500 focus:ring-neutral-500 sm:text-sm" placeholder="Used for server management"></textarea>
          </div>
        </div>


        <div class="flex flex-wrap gap-2">
          <button type="button" id="selectAllBtn" class="px-3 py-1 text-sm rounded-lg bg-neutral-100 dark:bg-neutral-700 border border-neutral-300 dark:border-neutral-600 hover:bg-neutral-200 dark:hover:bg-neutral-600 transition">Select All</button>
          <button type="button" id="selectNoneBtn" class="px-3 py-1 text-sm rounded-lg bg-neutral-100 dark:bg-neutral-700 border border-neutral-300 dark:border-neutral-600 hover:bg-neutral-200 dark:hover:bg-neutral-600 transition">Clear All</button>
          <button type="button" id="selectReadOnlyBtn" class="px-3 py-1 text-sm rounded-lg bg-neutral-100 dark:bg-neutral-700 border border-neutral-300 dark:border-neutral-600 hover:bg-neutral-200 dark:hover:bg-neutral-600 transition">Read-Only Access</button>
        </div>


        <div class="bg-neutral-50 dark:bg-neutral-900 p-4 rounded-xl border border-neutral-200 dark:border-neutral-700">
          <h4 class="text-sm font-semibold text-neutral-900 dark:text-white mb-3">Permissions</h4>
          <div class="space-y-3 max-h-96 overflow-y-auto pr-2">
            <% ['servers', 'users', 'nodes', 'settings'].forEach(category => { %>
              <div class="permission-category border rounded-lg border-neutral-200 dark:border-neutral-700">
                <button type="button" class="w-full flex justify-between items-center px-4 py-3 text-left text-sm font-medium text-neutral-700 dark:text-white bg-neutral-100 dark:bg-neutral-800 hover:bg-neutral-200 dark:hover:bg-neutral-700 transition" onclick="this.nextElementSibling.classList.toggle('hidden')">
                  <div class="flex items-center">
                    <% if (category === 'servers') { %>
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 mr-2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M5.25 14.25h13.5m-13.5 0a3 3 0 0 1-3-3m3 3a3 3 0 1 0 0 6h13.5a3 3 0 1 0 0-6m-16.5-3a3 3 0 0 1 3-3h13.5a3 3 0 0 1 3 3m-19.5 0a4.5 4.5 0 0 1 .9-2.7L5.737 5.1a3.375 3.375 0 0 1 2.7-1.35h7.126c1.062 0 2.062.5 2.7 1.35l2.587 3.45a4.5 4.5 0 0 1 .9 2.7m0 0a3 3 0 0 1-3 3m0 3h.008v.008h-.008v-.008Zm0-6h.008v.008h-.008v-.008Zm-3 6h.008v.008h-.008v-.008Zm0-6h.008v.008h-.008v-.008Z" />
                      </svg>
                    <% } else if (category === 'users') { %>
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 mr-2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z" />
                      </svg>
                    <% } else if (category === 'nodes') { %>
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 mr-2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M5.25 14.25h13.5m-13.5 0a3 3 0 0 1-3-3m3 3a3 3 0 1 0 0 6h13.5a3 3 0 1 0 0-6m-16.5-3a3 3 0 0 1 3-3h13.5a3 3 0 0 1 3 3m-19.5 0a4.5 4.5 0 0 1 .9-2.7L5.737 5.1a3.375 3.375 0 0 1 2.7-1.35h7.126c1.062 0 2.062.5 2.7 1.35l2.587 3.45a4.5 4.5 0 0 1 .9 2.7m0 0a3 3 0 0 1-3 3m0 3h.008v.008h-.008v-.008Zm0-6h.008v.008h-.008v-.008Zm-3 6h.008v.008h-.008v-.008Zm0-6h.008v.008h-.008v-.008Z" />
                      </svg>
                    <% } else if (category === 'settings') { %>
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 mr-2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z" />
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
                      </svg>
                    <% } %>
                    <span class="capitalize"><%= category %></span>
                    <div class="ml-1 relative group">
                      <div class="w-4 h-4 rounded-full bg-neutral-300 dark:bg-neutral-600 flex items-center justify-center text-xs text-neutral-700 dark:text-neutral-300 cursor-help">?</div>
                      <div class="absolute left-0 bottom-full mb-2 w-64 bg-white dark:bg-neutral-800 p-2 rounded-lg shadow-lg text-xs text-neutral-700 dark:text-neutral-300 hidden group-hover:block z-10 border border-neutral-200 dark:border-neutral-700">
                        <p><strong>Category:</strong> <%= category.charAt(0).toUpperCase() + category.slice(1) %></p>
                        <p class="mt-1"><strong>Description:</strong> Permissions for managing <%= category %> via the API.</p>
                      </div>
                    </div>
                  </div>
                  <span class="text-xs bg-neutral-200 dark:bg-neutral-700 px-2 py-1 rounded-lg flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-3 h-3 mr-1">
                      <path stroke-linecap="round" stroke-linejoin="round" d="m19.5 8.25-7.5 7.5-7.5-7.5" />
                    </svg>
                    Toggle
                  </span>
                </button>
                <div class="space-y-2 hidden p-3 border-t border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-900">
                  <% allPermissions.filter(p => p.value.includes(category)).forEach(permission => { %>
                    <div class="flex items-center">
                      <input id="permission-<%= permission.value %>" name="permissions" value="<%= permission.value %>" type="checkbox" class="h-4 w-4 rounded border-neutral-300 dark:border-neutral-700 focus:ring-neutral-500 permission-checkbox" data-category="<%= category %>">
                      <label for="permission-<%= permission.value %>" class="ml-2 text-sm text-neutral-900 dark:text-white"><%= permission.name %></label>
                      <div class="ml-1 relative group">
                        <div class="w-4 h-4 rounded-full bg-neutral-300 dark:bg-neutral-600 flex items-center justify-center text-xs text-neutral-700 dark:text-neutral-300 cursor-help">?</div>
                        <div class="absolute left-0 bottom-full mb-2 w-64 bg-white dark:bg-neutral-800 p-2 rounded-lg shadow-lg text-xs text-neutral-700 dark:text-neutral-300 hidden group-hover:block z-10 border border-neutral-200 dark:border-neutral-700">
                          <p><strong>Permission:</strong> <%= permission.value %></p>
                          <p class="mt-1"><strong>Description:</strong> Allows <%= permission.value.includes('read') ? 'viewing' : permission.value.includes('create') ? 'creating' : permission.value.includes('update') ? 'updating' : 'deleting' %> <%= permission.value.includes('servers') ? 'servers' : permission.value.includes('users') ? 'users' : permission.value.includes('nodes') ? 'nodes' : 'settings' %> via API.</p>
                        </div>
                      </div>
                    </div>
                  <% }); %>
                </div>
              </div>
            <% }); %>
          </div>
        </div>


        <div class="pt-4 flex justify-end space-x-3 border-t border-neutral-200 dark:border-neutral-700">
          <button type="button" onclick="hideCreateModal()" class="rounded-xl border border-neutral-300 dark:border-neutral-700 bg-white dark:bg-neutral-800 px-4 py-2 text-sm font-medium text-neutral-700 dark:text-neutral-300 hover:bg-neutral-100 dark:hover:bg-neutral-700">Cancel</button>
          <button type="submit" class="rounded-xl bg-neutral-950 dark:bg-white px-4 py-2 text-sm font-medium text-white dark:text-neutral-900 hover:bg-neutral-700 dark:hover:bg-neutral-200">Create</button>
        </div>

      </form>
    </div>
  </div>
</div>


<div id="editApiKeyModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
  <div class="flex min-h-full items-center justify-center p-4 text-center">
    <div class="relative transform overflow-hidden rounded-xl bg-white dark:bg-neutral-800 px-5 py-5 text-left shadow-2xl transition-all w-full max-w-3xl border border-neutral-200 dark:border-neutral-700 animate-fadeIn">
      <div class="flex justify-between items-center border-b border-neutral-200 dark:border-neutral-700 pb-3 mb-4">
        <h3 class="text-lg font-medium leading-6 text-neutral-900 dark:text-white">Edit API Key</h3>
        <button type="button" onclick="hideEditModal()" class="text-neutral-400 hover:text-neutral-500 focus:outline-none">
          <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
      <p class="text-sm text-neutral-500 mb-4">Update API key details and permissions.</p>
      <form id="editApiKeyForm" action="/admin/apikeys/edit/0" method="POST">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">

          <div class="md:col-span-1 space-y-4">
            <div class="bg-neutral-50 dark:bg-neutral-900 p-4 rounded-xl border border-neutral-200 dark:border-neutral-700">
              <h4 class="text-sm font-medium text-neutral-900 dark:text-white mb-3 border-b border-neutral-200 dark:border-neutral-700 pb-2">Key Information</h4>
              <div class="space-y-3">
                <div>
                  <label for="edit-name" class="block text-sm font-medium text-neutral-900 dark:text-white">Name</label>
                  <div class="mt-1">
                    <input type="text" name="name" id="edit-name" required class="block w-full rounded-xl border-neutral-300 dark:border-neutral-700 bg-white dark:bg-neutral-800 text-neutral-900 dark:text-white shadow-sm focus:border-neutral-500 focus:ring-neutral-500 sm:text-sm" placeholder="My API Key">
                  </div>
                </div>
                <div>
                  <label for="edit-description" class="block text-sm font-medium text-neutral-900 dark:text-white">Description</label>
                  <div class="mt-1">
                    <textarea name="description" id="edit-description" rows="3" class="block w-full rounded-xl border-neutral-300 dark:border-neutral-700 bg-white dark:bg-neutral-800 text-neutral-900 dark:text-white shadow-sm focus:border-neutral-500 focus:ring-neutral-500 sm:text-sm" placeholder="Used for server management"></textarea>
                  </div>
                </div>
              </div>
            </div>
            <div class="bg-neutral-50 dark:bg-neutral-900 p-4 rounded-xl border border-neutral-200 dark:border-neutral-700">
              <h4 class="text-sm font-medium text-neutral-900 dark:text-white mb-3 border-b border-neutral-200 dark:border-neutral-700 pb-2">Quick Select</h4>
              <div class="space-y-2">
                <button type="button" id="editSelectAllBtn" class="w-full text-left px-3 py-2 text-sm rounded-lg bg-white dark:bg-neutral-800 border border-neutral-300 dark:border-neutral-700 hover:bg-neutral-100 dark:hover:bg-neutral-700 transition-colors">
                  Select All Permissions
                </button>
                <button type="button" id="editSelectNoneBtn" class="w-full text-left px-3 py-2 text-sm rounded-lg bg-white dark:bg-neutral-800 border border-neutral-300 dark:border-neutral-700 hover:bg-neutral-100 dark:hover:bg-neutral-700 transition-colors">
                  Clear All Permissions
                </button>
                <button type="button" id="editSelectReadOnlyBtn" class="w-full text-left px-3 py-2 text-sm rounded-lg bg-white dark:bg-neutral-800 border border-neutral-300 dark:border-neutral-700 hover:bg-neutral-100 dark:hover:bg-neutral-700 transition-colors">
                  Read-Only Access
                </button>
              </div>
            </div>
          </div>


          <div class="md:col-span-2">
            <div class="bg-neutral-50 dark:bg-neutral-900 p-4 rounded-xl border border-neutral-200 dark:border-neutral-700">
              <h4 class="text-sm font-medium text-neutral-900 dark:text-white mb-3 border-b border-neutral-200 dark:border-neutral-700 pb-2">Permissions</h4>

              <div class="space-y-4 max-h-96 overflow-y-auto pr-2">

                <div class="permission-category border rounded-lg border-neutral-200 dark:border-neutral-700">
                  <div class="flex items-center justify-between px-4 py-3 bg-neutral-100 dark:bg-neutral-800 rounded-t-lg">
                    <div class="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 mr-2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M5.25 14.25h13.5m-13.5 0a3 3 0 0 1-3-3m3 3a3 3 0 1 0 0 6h13.5a3 3 0 1 0 0-6m-16.5-3a3 3 0 0 1 3-3h13.5a3 3 0 0 1 3 3m-19.5 0a4.5 4.5 0 0 1 .9-2.7L5.737 5.1a3.375 3.375 0 0 1 2.7-1.35h7.126c1.062 0 2.062.5 2.7 1.35l2.587 3.45a4.5 4.5 0 0 1 .9 2.7m0 0a3 3 0 0 1-3 3m0 3h.008v.008h-.008v-.008Zm0-6h.008v.008h-.008v-.008Zm-3 6h.008v.008h-.008v-.008Zm0-6h.008v.008h-.008v-.008Z" />
                      </svg>
                      <h5 class="text-sm font-medium text-neutral-700 dark:text-neutral-300">Servers</h5>
                      <div class="ml-1 relative group">
                        <div class="w-4 h-4 rounded-full bg-neutral-300 dark:bg-neutral-600 flex items-center justify-center text-xs text-neutral-700 dark:text-neutral-300 cursor-help">?</div>
                        <div class="absolute left-0 bottom-full mb-2 w-64 bg-white dark:bg-neutral-800 p-2 rounded-lg shadow-lg text-xs text-neutral-700 dark:text-neutral-300 hidden group-hover:block z-10 border border-neutral-200 dark:border-neutral-700">
                          <p><strong>Category:</strong> Servers</p>
                          <p class="mt-1"><strong>Description:</strong> Permissions for managing servers via the API.</p>
                        </div>
                      </div>
                    </div>
                    <div class="flex space-x-2">
                      <button type="button" class="edit-select-category text-xs px-2 py-1 rounded-lg bg-neutral-200 dark:bg-neutral-700 hover:bg-neutral-300 dark:hover:bg-neutral-600 transition-colors flex items-center" data-category="servers">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-3 h-3 mr-1">
                          <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" />
                        </svg>
                        Select All
                      </button>
                    </div>
                  </div>
                  <div class="grid grid-cols-1 sm:grid-cols-2 gap-2 pl-2 border-l-2 border-neutral-300 dark:border-neutral-700">
                    <% allPermissions.filter(p => p.value.includes('servers')).forEach(permission => { %>
                      <div class="flex items-center">
                        <input id="edit-permission-<%= permission.value %>" name="permissions" value="<%= permission.value %>" type="checkbox" class="h-4 w-4 rounded border-neutral-300 dark:border-neutral-700 text-neutral-600 dark:text-neutral-400 focus:ring-neutral-600 dark:focus:ring-neutral-400 edit-permission-checkbox" data-category="servers">
                        <label for="edit-permission-<%= permission.value %>" class="ml-2 block text-sm text-neutral-900 dark:text-white"><%= permission.name %></label>
                        <div class="ml-1 relative group">
                          <div class="w-4 h-4 rounded-full bg-neutral-300 dark:bg-neutral-600 flex items-center justify-center text-xs text-neutral-700 dark:text-neutral-300 cursor-help">?</div>
                          <div class="absolute left-0 bottom-full mb-2 w-64 bg-white dark:bg-neutral-800 p-2 rounded-lg shadow-lg text-xs text-neutral-700 dark:text-neutral-300 hidden group-hover:block z-10 border border-neutral-200 dark:border-neutral-700">
                            <p><strong>Permission:</strong> <%= permission.value %></p>
                            <p class="mt-1"><strong>Description:</strong> Allows <%= permission.value.includes('read') ? 'viewing' : permission.value.includes('create') ? 'creating' : permission.value.includes('update') ? 'updating' : 'deleting' %> <%= permission.value.includes('servers') ? 'servers' : permission.value.includes('users') ? 'users' : permission.value.includes('nodes') ? 'nodes' : 'settings' %> via API.</p>
                          </div>
                        </div>
                      </div>
                    <% }); %>
                  </div>
                </div>


                <div class="permission-category border rounded-lg border-neutral-200 dark:border-neutral-700">
                  <div class="flex items-center justify-between px-4 py-3 bg-neutral-100 dark:bg-neutral-800 rounded-t-lg">
                    <div class="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 mr-2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z" />
                      </svg>
                      <h5 class="text-sm font-medium text-neutral-700 dark:text-neutral-300">Users</h5>
                      <div class="ml-1 relative group">
                        <div class="w-4 h-4 rounded-full bg-neutral-300 dark:bg-neutral-600 flex items-center justify-center text-xs text-neutral-700 dark:text-neutral-300 cursor-help">?</div>
                        <div class="absolute left-0 bottom-full mb-2 w-64 bg-white dark:bg-neutral-800 p-2 rounded-lg shadow-lg text-xs text-neutral-700 dark:text-neutral-300 hidden group-hover:block z-10 border border-neutral-200 dark:border-neutral-700">
                          <p><strong>Category:</strong> Users</p>
                          <p class="mt-1"><strong>Description:</strong> Permissions for managing users via the API.</p>
                        </div>
                      </div>
                    </div>
                    <div class="flex space-x-2">
                      <button type="button" class="edit-select-category text-xs px-2 py-1 rounded-lg bg-neutral-200 dark:bg-neutral-700 hover:bg-neutral-300 dark:hover:bg-neutral-600 transition-colors flex items-center" data-category="users">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-3 h-3 mr-1">
                          <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" />
                        </svg>
                        Select All
                      </button>
                    </div>
                  </div>
                  <div class="grid grid-cols-1 sm:grid-cols-2 gap-2 pl-2 border-l-2 border-neutral-300 dark:border-neutral-700">
                    <% allPermissions.filter(p => p.value.includes('users')).forEach(permission => { %>
                      <div class="flex items-center">
                        <input id="edit-permission-<%= permission.value %>" name="permissions" value="<%= permission.value %>" type="checkbox" class="h-4 w-4 rounded border-neutral-300 dark:border-neutral-700 text-neutral-600 dark:text-neutral-400 focus:ring-neutral-600 dark:focus:ring-neutral-400 edit-permission-checkbox" data-category="users">
                        <label for="edit-permission-<%= permission.value %>" class="ml-2 block text-sm text-neutral-900 dark:text-white"><%= permission.name %></label>
                        <div class="ml-1 relative group">
                          <div class="w-4 h-4 rounded-full bg-neutral-300 dark:bg-neutral-600 flex items-center justify-center text-xs text-neutral-700 dark:text-neutral-300 cursor-help">?</div>
                          <div class="absolute left-0 bottom-full mb-2 w-64 bg-white dark:bg-neutral-800 p-2 rounded-lg shadow-lg text-xs text-neutral-700 dark:text-neutral-300 hidden group-hover:block z-10 border border-neutral-200 dark:border-neutral-700">
                            <p><strong>Permission:</strong> <%= permission.value %></p>
                            <p class="mt-1"><strong>Description:</strong> Allows <%= permission.value.includes('read') ? 'viewing' : permission.value.includes('create') ? 'creating' : permission.value.includes('update') ? 'updating' : 'deleting' %> <%= permission.value.includes('servers') ? 'servers' : permission.value.includes('users') ? 'users' : permission.value.includes('nodes') ? 'nodes' : 'settings' %> via API.</p>
                          </div>
                        </div>
                      </div>
                    <% }); %>
                  </div>
                </div>


                <div class="permission-category border rounded-lg border-neutral-200 dark:border-neutral-700">
                  <div class="flex items-center justify-between px-4 py-3 bg-neutral-100 dark:bg-neutral-800 rounded-t-lg">
                    <div class="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 mr-2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M5.25 14.25h13.5m-13.5 0a3 3 0 0 1-3-3m3 3a3 3 0 1 0 0 6h13.5a3 3 0 1 0 0-6m-16.5-3a3 3 0 0 1 3-3h13.5a3 3 0 0 1 3 3m-19.5 0a4.5 4.5 0 0 1 .9-2.7L5.737 5.1a3.375 3.375 0 0 1 2.7-1.35h7.126c1.062 0 2.062.5 2.7 1.35l2.587 3.45a4.5 4.5 0 0 1 .9 2.7m0 0a3 3 0 0 1-3 3m0 3h.008v.008h-.008v-.008Zm0-6h.008v.008h-.008v-.008Zm-3 6h.008v.008h-.008v-.008Zm0-6h.008v.008h-.008v-.008Z" />
                      </svg>
                      <h5 class="text-sm font-medium text-neutral-700 dark:text-neutral-300">Nodes</h5>
                      <div class="ml-1 relative group">
                        <div class="w-4 h-4 rounded-full bg-neutral-300 dark:bg-neutral-600 flex items-center justify-center text-xs text-neutral-700 dark:text-neutral-300 cursor-help">?</div>
                        <div class="absolute left-0 bottom-full mb-2 w-64 bg-white dark:bg-neutral-800 p-2 rounded-lg shadow-lg text-xs text-neutral-700 dark:text-neutral-300 hidden group-hover:block z-10 border border-neutral-200 dark:border-neutral-700">
                          <p><strong>Category:</strong> Nodes</p>
                          <p class="mt-1"><strong>Description:</strong> Permissions for managing nodes via the API.</p>
                        </div>
                      </div>
                    </div>
                    <div class="flex space-x-2">
                      <button type="button" class="edit-select-category text-xs px-2 py-1 rounded-lg bg-neutral-200 dark:bg-neutral-700 hover:bg-neutral-300 dark:hover:bg-neutral-600 transition-colors flex items-center" data-category="nodes">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-3 h-3 mr-1">
                          <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" />
                        </svg>
                        Select All
                      </button>
                    </div>
                  </div>
                  <div class="grid grid-cols-1 sm:grid-cols-2 gap-2 pl-2 border-l-2 border-neutral-300 dark:border-neutral-700">
                    <% allPermissions.filter(p => p.value.includes('nodes')).forEach(permission => { %>
                      <div class="flex items-center">
                        <input id="edit-permission-<%= permission.value %>" name="permissions" value="<%= permission.value %>" type="checkbox" class="h-4 w-4 rounded border-neutral-300 dark:border-neutral-700 text-neutral-600 dark:text-neutral-400 focus:ring-neutral-600 dark:focus:ring-neutral-400 edit-permission-checkbox" data-category="nodes">
                        <label for="edit-permission-<%= permission.value %>" class="ml-2 block text-sm text-neutral-900 dark:text-white"><%= permission.name %></label>
                        <div class="ml-1 relative group">
                          <div class="w-4 h-4 rounded-full bg-neutral-300 dark:bg-neutral-600 flex items-center justify-center text-xs text-neutral-700 dark:text-neutral-300 cursor-help">?</div>
                          <div class="absolute left-0 bottom-full mb-2 w-64 bg-white dark:bg-neutral-800 p-2 rounded-lg shadow-lg text-xs text-neutral-700 dark:text-neutral-300 hidden group-hover:block z-10 border border-neutral-200 dark:border-neutral-700">
                            <p><strong>Permission:</strong> <%= permission.value %></p>
                            <p class="mt-1"><strong>Description:</strong> Allows <%= permission.value.includes('read') ? 'viewing' : permission.value.includes('create') ? 'creating' : permission.value.includes('update') ? 'updating' : 'deleting' %> <%= permission.value.includes('servers') ? 'servers' : permission.value.includes('users') ? 'users' : permission.value.includes('nodes') ? 'nodes' : 'settings' %> via API.</p>
                          </div>
                        </div>
                      </div>
                    <% }); %>
                  </div>
                </div>


                <div class="permission-category border rounded-lg border-neutral-200 dark:border-neutral-700">
                  <div class="flex items-center justify-between px-4 py-3 bg-neutral-100 dark:bg-neutral-800 rounded-t-lg">
                    <div class="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 mr-2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z" />
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
                      </svg>
                      <h5 class="text-sm font-medium text-neutral-700 dark:text-neutral-300">Settings</h5>
                      <div class="ml-1 relative group">
                        <div class="w-4 h-4 rounded-full bg-neutral-300 dark:bg-neutral-600 flex items-center justify-center text-xs text-neutral-700 dark:text-neutral-300 cursor-help">?</div>
                        <div class="absolute left-0 bottom-full mb-2 w-64 bg-white dark:bg-neutral-800 p-2 rounded-lg shadow-lg text-xs text-neutral-700 dark:text-neutral-300 hidden group-hover:block z-10 border border-neutral-200 dark:border-neutral-700">
                          <p><strong>Category:</strong> Settings</p>
                          <p class="mt-1"><strong>Description:</strong> Permissions for managing panel settings via the API.</p>
                        </div>
                      </div>
                    </div>
                    <div class="flex space-x-2">
                      <button type="button" class="edit-select-category text-xs px-2 py-1 rounded-lg bg-neutral-200 dark:bg-neutral-700 hover:bg-neutral-300 dark:hover:bg-neutral-600 transition-colors flex items-center" data-category="settings">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-3 h-3 mr-1">
                          <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" />
                        </svg>
                        Select All
                      </button>
                    </div>
                  </div>
                  <div class="grid grid-cols-1 sm:grid-cols-2 gap-2 pl-2 border-l-2 border-neutral-300 dark:border-neutral-700">
                    <% allPermissions.filter(p => p.value.includes('settings')).forEach(permission => { %>
                      <div class="flex items-center">
                        <input id="edit-permission-<%= permission.value %>" name="permissions" value="<%= permission.value %>" type="checkbox" class="h-4 w-4 rounded border-neutral-300 dark:border-neutral-700 text-neutral-600 dark:text-neutral-400 focus:ring-neutral-600 dark:focus:ring-neutral-400 edit-permission-checkbox" data-category="settings">
                        <label for="edit-permission-<%= permission.value %>" class="ml-2 block text-sm text-neutral-900 dark:text-white"><%= permission.name %></label>
                      </div>
                    <% }); %>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="mt-6 flex justify-end space-x-3 border-t border-neutral-200 dark:border-neutral-700 pt-4">
          <button type="button" onclick="hideEditModal()" class="inline-flex justify-center rounded-xl border border-neutral-300 dark:border-neutral-700 bg-white dark:bg-neutral-800 px-4 py-2 text-sm font-medium text-neutral-700 dark:text-neutral-300 shadow-sm hover:bg-neutral-50 dark:hover:bg-neutral-700 focus:outline-none">Cancel</button>
          <button type="submit" class="inline-flex justify-center rounded-xl bg-neutral-950 dark:bg-white px-4 py-2 text-sm font-medium text-white dark:text-neutral-900 shadow-sm hover:bg-neutral-700 dark:hover:bg-neutral-200 focus:outline-none">Update</button>
        </div>
      </form>
    </div>
  </div>
</div>

<div id="modal-backdrop" class="fixed inset-0 bg-neutral-900 bg-opacity-80 backdrop-blur-sm transition-opacity hidden"></div>

<script>

  function showToast(message, type = 'success') {
    const toast = document.createElement('div');
    toast.className = `fixed bottom-4 right-4 z-50 px-4 py-2 rounded-lg shadow-lg transition-opacity duration-500 ${type === 'success' ? 'bg-green-600 text-white' : 'bg-red-600 text-white'}`;
    toast.textContent = message;
    document.body.appendChild(toast);


    setTimeout(() => {
      toast.style.opacity = '1';
    }, 10);


    setTimeout(() => {
      toast.style.opacity = '0';
      setTimeout(() => {
        document.body.removeChild(toast);
      }, 500);
    }, 3000);
  }


  function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
      showToast('API key copied to clipboard');
    }).catch(err => {
      console.error('Could not copy text: ', err);
      showToast('Failed to copy to clipboard', 'error');
    });
  }


  const createApiKeyBtn = document.getElementById('createApiKeyBtn');
  const createApiKeyModal = document.getElementById('createApiKeyModal');
  const modalBackdrop = document.getElementById('modal-backdrop');

  createApiKeyBtn.addEventListener('click', () => {
    createApiKeyModal.classList.remove('hidden');
    modalBackdrop.classList.remove('hidden');
  });

  function hideCreateModal() {
    createApiKeyModal.classList.add('hidden');
    modalBackdrop.classList.add('hidden');
  }


  const editApiKeyModal = document.getElementById('editApiKeyModal');
  const editApiKeyForm = document.getElementById('editApiKeyForm');
  const editNameInput = document.getElementById('edit-name');
  const editDescriptionInput = document.getElementById('edit-description');

  function showEditModal(id, name, description, permissions) {
    editApiKeyForm.action = `/admin/apikeys/edit/${id}`;
    editNameInput.value = name;
    editDescriptionInput.value = description;


    document.querySelectorAll('[id^="edit-permission-"]').forEach(checkbox => {
      checkbox.checked = false;
    });


    try {
      const permissionsArray = typeof permissions === 'string' ? JSON.parse(permissions) : permissions;
      permissionsArray.forEach(permission => {
        const checkbox = document.getElementById(`edit-permission-${permission}`);
        if (checkbox) {
          checkbox.checked = true;
        }
      });
    } catch (e) {
      console.error('Error parsing permissions:', e);
    }

    editApiKeyModal.classList.remove('hidden');
    modalBackdrop.classList.remove('hidden');
  }

  function hideEditModal() {
    editApiKeyModal.classList.add('hidden');
    modalBackdrop.classList.add('hidden');
  }


  modalBackdrop.addEventListener('click', () => {
    hideCreateModal();
    hideEditModal();
  });


  document.querySelectorAll('.select-category').forEach(button => {
    button.addEventListener('click', function() {
      const category = this.getAttribute('data-category');
      const checkboxes = document.querySelectorAll(`.permission-checkbox[data-category="${category}"]`);


      const allSelected = Array.from(checkboxes).every(cb => cb.checked);


      checkboxes.forEach(checkbox => {
        checkbox.checked = !allSelected;
      });


      this.textContent = allSelected ? 'Select All' : 'Deselect All';
    });
  });

  document.querySelectorAll('.edit-select-category').forEach(button => {
    button.addEventListener('click', function() {
      const category = this.getAttribute('data-category');
      const checkboxes = document.querySelectorAll(`.edit-permission-checkbox[data-category="${category}"]`);


      const allSelected = Array.from(checkboxes).every(cb => cb.checked);


      checkboxes.forEach(checkbox => {
        checkbox.checked = !allSelected;
      });


      this.textContent = allSelected ? 'Select All' : 'Deselect All';
    });
  });


  document.getElementById('selectAllBtn').addEventListener('click', function() {
    document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
      checkbox.checked = true;
    });
    showToast('All permissions selected');
  });

  document.getElementById('selectNoneBtn').addEventListener('click', function() {
    document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
      checkbox.checked = false;
    });
    showToast('All permissions cleared');
  });

  document.getElementById('selectReadOnlyBtn').addEventListener('click', function() {

    document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
      checkbox.checked = false;
    });


    document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
      if (checkbox.value.includes('.read')) {
        checkbox.checked = true;
      }
    });

    showToast('Read-only permissions selected');
  });


  document.getElementById('editSelectAllBtn').addEventListener('click', function() {
    document.querySelectorAll('.edit-permission-checkbox').forEach(checkbox => {
      checkbox.checked = true;
    });
    showToast('All permissions selected');
  });

  document.getElementById('editSelectNoneBtn').addEventListener('click', function() {
    document.querySelectorAll('.edit-permission-checkbox').forEach(checkbox => {
      checkbox.checked = false;
    });
    showToast('All permissions cleared');
  });

  document.getElementById('editSelectReadOnlyBtn').addEventListener('click', function() {

    document.querySelectorAll('.edit-permission-checkbox').forEach(checkbox => {
      checkbox.checked = false;
    });


    document.querySelectorAll('.edit-permission-checkbox').forEach(checkbox => {
      if (checkbox.value.includes('.read')) {
        checkbox.checked = true;
      }
    });

    showToast('Read-only permissions selected');
  });
</script>

<%- include('../../components/footer') %>
