<%- include('../../components/header', { title: req.translations.adminEditUserTitle || 'Edit User' }) %>

<main class="h-screen m-auto">
  <div class="flex h-screen">
    <!-- Sidebar -->
    <div class="w-60 h-full">
      <%- include('../../components/template') %>
    </div>
    <!-- Content -->
    <div class="flex-1 p-6 overflow-y-auto pt-16">
      <div class="sm:flex sm:items-center px-8 pt-4">
        <div class="sm:flex-auto">
          <h1 class="text-base font-medium leading-6 text-white"><%= req.translations.adminEditUserTitle || 'Edit User' %></h1>
          <p class="mt-1 tracking-tight text-sm text-neutral-500"><%= req.translations.adminEditUserText || 'Edit user details and permissions.' %></p>
        </div>
        <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
          <a href="/admin/users" class="inline-flex items-center rounded-xl bg-neutral-950 dark:bg-white hover:bg-neutral-300 text-neutral-200 dark:text-neutral-800 px-3 py-2 text-sm font-medium shadow-md transition focus:outline focus:outline-2 focus:outline-offset-2">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            <%= req.translations.back || 'Back to Users' %>
          </a>
        </div>
      </div>

      <div id="userForm" class="mt-6 px-8 w-full">
        <div class="bg-white/5 rounded-xl p-6 shadow-lg border border-neutral-800/20">
          <form id="editUserForm">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- User Information Section -->
              <div class="space-y-6">
                <h2 class="text-lg font-medium text-white border-b border-white/10 pb-2">
                  <%= req.translations.userInformation || 'User Information' %>
                </h2>

                <!-- Username -->
                <div>
                  <label for="username" class="block text-white text-sm font-medium mb-2">
                    <%= req.translations.username || 'Username' %>
                  </label>
                  <input
                    type="text"
                    id="username"
                    name="username"
                    value="<%= dataUser.username %>"
                    class="rounded-xl focus:ring focus:ring-neutral-800/10 focus:border-neutral-800/20 text-neutral-800 dark:text-white text-sm mt-2 mb-4 w-full hover:bg-white/5 px-4 py-2 bg-neutral-400/10 dark:bg-neutral-600/20 placeholder:text-neutral-950/50 dark:placeholder:text-white/20 border border-neutral-800/10 dark:border-white/5"
                    required
                  >
                </div>

                <!-- Email -->
                <div>
                  <label for="email" class="block text-white text-sm font-medium mb-2">
                    <%= req.translations.email || 'Email' %>
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value="<%= dataUser.email %>"
                    class="rounded-xl focus:ring focus:ring-neutral-800/10 focus:border-neutral-800/20 text-neutral-800 dark:text-white text-sm mt-2 mb-4 w-full hover:bg-white/5 px-4 py-2 bg-neutral-400/10 dark:bg-neutral-600/20 placeholder:text-neutral-950/50 dark:placeholder:text-white/20 border border-neutral-800/10 dark:border-white/5"
                    required
                  >
                </div>

                <!-- Description -->
                <div>
                  <label for="description" class="block text-white text-sm font-medium mb-2">
                    <%= req.translations.description || 'Description' %>
                  </label>
                  <textarea
                    id="description"
                    name="description"
                    rows="3"
                    class="rounded-xl focus:ring focus:ring-neutral-800/10 focus:border-neutral-800/20 text-neutral-800 dark:text-white text-sm mt-2 mb-4 w-full hover:bg-white/5 px-4 py-2 bg-neutral-400/10 dark:bg-neutral-600/20 placeholder:text-neutral-950/50 dark:placeholder:text-white/20 border border-neutral-800/10 dark:border-white/5"
                  ><%= dataUser.description || '' %></textarea>
                </div>
              </div>

              <!-- Security Section -->
              <div class="space-y-6">
                <h2 class="text-lg font-medium text-white border-b border-white/10 pb-2">
                  <%= req.translations.security || 'Security' %>
                </h2>

                <!-- Password -->
                <div>
                  <label for="password" class="block text-white text-sm font-medium mb-2">
                    <%= req.translations.newPassword || 'New Password' %> <span class="text-neutral-400 text-xs">(<%= req.translations.leaveBlankToKeep || 'Leave blank to keep current password' %>)</span>
                  </label>
                  <input
                    type="password"
                    id="password"
                    name="password"
                    class="rounded-xl focus:ring focus:ring-neutral-800/10 focus:border-neutral-800/20 text-neutral-800 dark:text-white text-sm mt-2 mb-4 w-full hover:bg-white/5 px-4 py-2 bg-neutral-400/10 dark:bg-neutral-600/20 placeholder:text-neutral-950/50 dark:placeholder:text-white/20 border border-neutral-800/10 dark:border-white/5"
                  >
                </div>

                <!-- Confirm Password -->
                <div>
                  <label for="confirmPassword" class="block text-white text-sm font-medium mb-2">
                    <%= req.translations.confirmPassword || 'Confirm Password' %>
                  </label>
                  <input
                    type="password"
                    id="confirmPassword"
                    name="confirmPassword"
                    class="rounded-xl focus:ring focus:ring-neutral-800/10 focus:border-neutral-800/20 text-neutral-800 dark:text-white text-sm mt-2 mb-4 w-full hover:bg-white/5 px-4 py-2 bg-neutral-400/10 dark:bg-neutral-600/20 placeholder:text-neutral-950/50 dark:placeholder:text-white/20 border border-neutral-800/10 dark:border-white/5"
                  >
                </div>

                <!-- Admin Status -->
                <div class="mt-6">
                  <div class="flex items-center">
                    <div class="bg-neutral-400/10 dark:bg-neutral-600/20 p-4 rounded-xl border border-neutral-800/10 dark:border-white/5 w-full">
                      <div class="flex items-center justify-between">
                        <div>
                          <h3 class="text-sm font-medium text-white">
                            <%= req.translations.adminStatus || 'Administrator Status' %>
                          </h3>
                          <p class="text-xs text-neutral-400 mt-1">
                            <%= req.translations.adminDescription || 'Administrators have full access to manage all aspects of the system.' %>
                          </p>
                        </div>
                        <label for="userIsAdminSwitch" class="relative inline-block w-12 h-6">
                          <input type="checkbox" id="isAdmin" name="isAdmin" class="sr-only peer" <%= dataUser.isAdmin ? 'checked' : '' %>>
                          <span class="block w-12 h-6 bg-neutral-400 peer-checked:bg-blue-500 rounded-full"></span>
                          <span class="absolute left-0.5 top-0.5 w-5 h-5 bg-white rounded-full peer-checked:translate-x-6 transition"></span>
                        </label>
                        <span class="ms-3 text-sm font-medium text-white" id="adminStatusLabel"><%= dataUser.isAdmin ? req.translations.enabled || 'Enabled' : req.translations.disabled || 'Disabled' %></span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Submit Button -->
            <div class="mt-8 flex justify-end">
              <button
                type="submit"
                class="md:w-auto rounded-xl bg-neutral-950 dark:bg-white hover:bg-neutral-300 text-neutral-200 dark:text-neutral-800 px-6 py-2 text-sm font-medium shadow-md transition focus:outline focus:outline-2 focus:outline-offset-2"
              >
                <%= req.translations.saveChanges || 'Save Changes' %>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</main>

<%- include('../../components/toast') %>
<%- include('../../components/loadingPopup') %>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('editUserForm');
    const isAdminToggle = document.getElementById('isAdmin');
    const adminStatusLabel = document.getElementById('adminStatusLabel');

    // Update admin status label when toggle changes
    isAdminToggle.addEventListener('change', function() {
      adminStatusLabel.textContent = this.checked
        ? '<%= req.translations.enabled || "Enabled" %>'
        : '<%= req.translations.disabled || "Disabled" %>';
    });

    // Handle form submission
    form.addEventListener('submit', async function(e) {
      e.preventDefault();

      // Validate passwords match if new password is provided
      const password = document.getElementById('password').value;
      const confirmPassword = document.getElementById('confirmPassword').value;

      if (password && password !== confirmPassword) {
        showToast('<%= req.translations.passwordsDoNotMatch || "Passwords do not match" %>', 'error');
        return;
      }

      // Collect form data
      const formData = new FormData(form);
      const data = {};

      for (const [key, value] of formData.entries()) {
        // Skip confirmPassword as it's not needed for the API
        if (key !== 'confirmPassword') {
          data[key] = value;
        }
      }

      // Handle checkbox value
      data.isAdmin = isAdminToggle.checked;

      // Show loading popup
      const loader = showLoadingPopup('<%= req.translations.updatingUser || "Updating User" %>', '<%= req.translations.processingUserUpdate || "Processing user update..." %>');
      loader.updateProgress(20, '<%= req.translations.sendingUserInformation || "Sending user information..." %>');

      try {
        // Send update request
        const response = await fetch('/admin/users/update/<%= dataUser.id %>/', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data),
        });

        const responseData = await response.json();

        if (responseData.error) {
          loader.close();
          showToast(responseData.error, 'error');
        } else {
          loader.updateProgress(100, '<%= req.translations.userUpdatedSuccessfully || "User updated successfully!" %>');
          setTimeout(() => {
            loader.close();
            showToast(responseData.message || '<%= req.translations.userUpdatedSuccessfully || "User updated successfully" %>', 'success');

            // Redirect back to users list after a short delay
            setTimeout(() => {
              window.location.href = '/admin/users';
            }, 1000);
          }, 500);
        }
      } catch (error) {
        loader.close();
        console.error('Error:', error);
        showToast('<%= req.translations.errorUpdatingUser || "Error updating user" %>', 'error');
      }
    });
  });
</script>

<%- include('../../components/footer') %>
