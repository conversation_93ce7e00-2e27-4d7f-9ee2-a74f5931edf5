<!-- SPA Test Page Content -->
<div class="px-4 sm:px-6 lg:px-8 mt-8">
  <div class="sm:flex sm:items-center">
    <div class="sm:flex-auto">
      <h1 class="text-base font-medium leading-6 text-neutral-800 dark:text-white">SPA Test Page</h1>
      <p class="mt-1 tracking-tight text-sm text-neutral-500">Testing Single Page Application functionality</p>
    </div>
  </div>

  <!-- Navigation Test -->
  <div class="mt-8 bg-white dark:bg-neutral-800/50 shadow rounded-xl p-6 border border-gray-200 dark:border-neutral-700/30">
    <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Navigation Test</h2>
    <div class="space-y-3">
      <div>
        <a href="/" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z" />
          </svg>
          Go to Dashboard (SPA Navigation)
        </a>
      </div>
      <div>
        <button onclick="window.spaRouter.navigate('/admin/overview')" class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-lg transition-colors">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2-2V7a2 2 0 012-2h2a2 2 0 002 2v2a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 012-2h2a2 2 0 002 2v2a2 2 0 00-2 2h-2a2 2 0 00-2 2v6a2 2 0 01-2 2H9z" />
          </svg>
          Admin Overview (Programmatic Navigation)
        </button>
      </div>
    </div>
  </div>

  <!-- Preloading Test -->
  <div class="mt-8 bg-white dark:bg-neutral-800/50 shadow rounded-xl p-6 border border-gray-200 dark:border-neutral-700/30">
    <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Preloading Test</h2>
    <p class="text-sm text-gray-600 dark:text-neutral-400 mb-4">Hover over these links to test preloading functionality:</p>
    <div class="space-y-2">
      <div>
        <a href="/admin/users" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 underline">
          Admin Users (Hover to preload)
        </a>
      </div>
      <div>
        <a href="/admin/servers" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 underline">
          Admin Servers (Hover to preload)
        </a>
      </div>
    </div>
  </div>

  <!-- Component Persistence Test -->
  <div class="mt-8 bg-white dark:bg-neutral-800/50 shadow rounded-xl p-6 border border-gray-200 dark:border-neutral-700/30">
    <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Component Persistence Test</h2>
    <p class="text-sm text-gray-600 dark:text-neutral-400 mb-4">The sidebar and topbar should remain unchanged during navigation.</p>
    
    <div class="space-y-4">
      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">Test Form (should persist state):</label>
        <input type="text" id="test-input" class="block w-full px-3 py-2 border border-gray-300 dark:border-neutral-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-neutral-700 dark:text-white" placeholder="Type something and navigate away...">
      </div>
      
      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">Scroll Position Test:</label>
        <div class="h-32 overflow-y-auto border border-gray-300 dark:border-neutral-600 rounded-md p-4 bg-gray-50 dark:bg-neutral-700">
          <div class="space-y-2">
            <% for (let i = 1; i <= 20; i++) { %>
              <p class="text-sm text-gray-600 dark:text-neutral-400">Scroll test line <%= i %> - This content should maintain scroll position during navigation.</p>
            <% } %>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Cache and Performance Test -->
  <div class="mt-8 bg-white dark:bg-neutral-800/50 shadow rounded-xl p-6 border border-gray-200 dark:border-neutral-700/30">
    <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Cache and Performance</h2>
    <div class="space-y-4">
      <div>
        <button onclick="showCacheStats()" class="inline-flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white text-sm font-medium rounded-lg transition-colors">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2-2V7a2 2 0 012-2h2a2 2 0 002 2v2a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 012-2h2a2 2 0 002 2v2a2 2 0 00-2 2h-2a2 2 0 00-2 2v6a2 2 0 01-2 2H9z" />
          </svg>
          Show Cache Statistics
        </button>
      </div>
      
      <div>
        <button onclick="clearAllCaches()" class="inline-flex items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded-lg transition-colors">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
          Clear All Caches
        </button>
      </div>
      
      <div id="cache-stats" class="mt-4 p-4 bg-gray-100 dark:bg-neutral-700 rounded-lg hidden">
        <h3 class="font-medium text-gray-900 dark:text-white mb-2">Cache Statistics:</h3>
        <pre id="cache-stats-content" class="text-xs text-gray-600 dark:text-neutral-400 whitespace-pre-wrap"></pre>
      </div>
    </div>
  </div>

  <!-- Error Handling Test -->
  <div class="mt-8 bg-white dark:bg-neutral-800/50 shadow rounded-xl p-6 border border-gray-200 dark:border-neutral-700/30">
    <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Error Handling Test</h2>
    <div class="space-y-3">
      <div>
        <button onclick="window.spaRouter.navigate('/nonexistent-page')" class="inline-flex items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded-lg transition-colors">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
          Test 404 Error
        </button>
      </div>
    </div>
  </div>
</div>

<script>
function showCacheStats() {
  const statsContainer = document.getElementById('cache-stats');
  const statsContent = document.getElementById('cache-stats-content');
  
  if (window.spaRouter && window.preloader) {
    const routerStats = window.spaRouter.getCacheSize();
    const preloaderStats = window.preloader.getPreloadStats();
    
    const stats = {
      router: routerStats,
      preloader: preloaderStats,
      timestamp: new Date().toISOString()
    };
    
    statsContent.textContent = JSON.stringify(stats, null, 2);
    statsContainer.classList.remove('hidden');
  } else {
    statsContent.textContent = 'SPA Router or Preloader not available';
    statsContainer.classList.remove('hidden');
  }
}

function clearAllCaches() {
  if (window.spaRouter) {
    window.spaRouter.clearCache();
  }
  if (window.preloader) {
    window.preloader.clearPreloadCache();
  }
  alert('All caches cleared!');
}

// Test component persistence
document.addEventListener('DOMContentLoaded', function() {
  const testInput = document.getElementById('test-input');
  if (testInput) {
    // Save input value to localStorage
    testInput.addEventListener('input', function() {
      localStorage.setItem('spa-test-input', this.value);
    });
    
    // Restore input value
    const savedValue = localStorage.getItem('spa-test-input');
    if (savedValue) {
      testInput.value = savedValue;
    }
  }
});
</script>
