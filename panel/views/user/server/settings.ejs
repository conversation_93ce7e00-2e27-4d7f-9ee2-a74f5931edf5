<%- include('../../components/header', { title: 'Server Settings' }) %>

<main class="h-screen m-auto text-white">
  <div class="flex h-screen">
    <!-- Sidebar -->
    <aside class="w-60 h-full">
      <%- include('../../components/template') %>
    </aside>

    <!-- Main Content -->
    <section class="flex-1 p-6 overflow-y-auto pt-16">
      <!-- Page Header -->
      <header class="sm:flex sm:items-center px-8 pt-4">
        <%- include('../../components/serverHeader') %>
      </header>

      <%- include('../../components/installHeader') %>

      <!-- Server Template -->
      <%- include('../../components/serverTemplate') %>

      <!-- Server Settings -->
      <div class="px-8 mt-8">
        <div class="bg-white/5 rounded-xl p-6 shadow-lg border border-neutral-800/20">
          <h2 class="text-lg font-semibold mb-4 text-white">Server Settings</h2>
          <p class="text-sm text-neutral-400 mb-4">Configure your server's basic settings.</p>

          <form id="serverSettingsForm">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label for="serverName" class="block text-sm font-medium text-neutral-300 mb-2">Server Name</label>
                <input type="text" id="serverName" name="name" value="<%= server.name %>" class="w-full rounded-xl focus:ring focus:ring-neutral-800/10 focus:border-neutral-800/20 text-white text-sm px-4 py-2 bg-neutral-600/20 border border-white/5">
              </div>

              <div>
                <label for="serverDescription" class="block text-sm font-medium text-neutral-300 mb-2">Description</label>
                <input type="text" id="serverDescription" name="description" value="<%= server.description || '' %>" class="w-full rounded-xl focus:ring focus:ring-neutral-800/10 focus:border-neutral-800/20 text-white text-sm px-4 py-2 bg-neutral-600/20 border border-white/5">
              </div>
            </div>

            <div class="mt-6">
              <button type="submit" class="rounded-xl bg-blue-600 px-4 py-2 text-center text-sm font-medium text-white shadow-lg hover:bg-blue-500 transition">Save Settings</button>
            </div>
          </form>
        </div>

        <!-- Server Information -->
        <div class="bg-white/5 rounded-xl p-6 shadow-lg border border-neutral-800/20 mt-8">
          <h2 class="text-lg font-semibold mb-4 text-white">Server Information</h2>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 class="text-sm font-medium text-neutral-300 mb-2">Server ID</h3>
              <p class="text-neutral-400"><%= server.UUID %></p>
            </div>

            <div>
              <h3 class="text-sm font-medium text-neutral-300 mb-2">Created</h3>
              <p class="text-neutral-400"><%= new Date(server.createdAt).toLocaleString() %></p>
            </div>

            <div>
              <h3 class="text-sm font-medium text-neutral-300 mb-2">Node</h3>
              <p class="text-neutral-400"><%= server.node.name %></p>
            </div>

            <div>
              <h3 class="text-sm font-medium text-neutral-300 mb-2">Image</h3>
              <p class="text-neutral-400"><%= server.image.name || 'Unknown' %></p>
            </div>

            <div>
              <h3 class="text-sm font-medium text-neutral-300 mb-2">Memory</h3>
              <p class="text-neutral-400"><%= server.Memory %> GB</p>
            </div>

            <div>
              <h3 class="text-sm font-medium text-neutral-300 mb-2">CPU</h3>
              <p class="text-neutral-400"><%= server.Cpu %>%</p>
            </div>

            <div>
              <h3 class="text-sm font-medium text-neutral-300 mb-2">Storage</h3>
              <p class="text-neutral-400"><%= server.Storage %> GB</p>
            </div>

            <div>
              <h3 class="text-sm font-medium text-neutral-300 mb-2">Status</h3>
              <p class="text-neutral-400">
                <span class="inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset
                  <%= server.Suspended ? 'bg-yellow-600/10 text-yellow-400 ring-yellow-600/20' : 'bg-emerald-600/10 text-emerald-400 ring-emerald-600/20' %>">
                  <%= server.Suspended ? 'Suspended' : 'Active' %>
                </span>
              </p>
            </div>
          </div>
        </div>

        <!-- Danger Zone -->
        <div class="bg-red-950/20 rounded-xl p-6 shadow-lg border border-red-800/20 mt-8">
          <h2 class="text-lg font-semibold mb-4 text-white">Danger Zone</h2>

          <div class="flex items-center justify-between mb-6">
            <div>
              <h3 class="text-sm font-medium text-neutral-300">Restart Server</h3>
              <p class="text-sm text-neutral-400">Force a restart of your server</p>
            </div>
            <button id="restartButton" type="button" class="w-full md:w-auto rounded-xl bg-red-600 hover:bg-red-500 text-white px-3 py-2 text-sm font-medium shadow-md transition focus:outline focus:outline-2 focus:outline-offset-2">Restart</button>
          </div>

          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-sm font-medium text-neutral-300">Reinstall Server</h3>
              <p class="text-sm text-neutral-400">Reinstall your server from scratch (all data will be lost)</p>
            </div>
            <button id="reinstallButton" type="button" class="w-full md:w-auto rounded-xl bg-red-600 hover:bg-red-500 text-white px-3 py-2 text-sm font-medium shadow-md transition focus:outline focus:outline-2 focus:outline-offset-2">Reinstall</button>
          </div>
        </div>
      </div>
    </section>
  </div>
</main>

<%- include('../../components/toast') %>
<%- include('../../components/loadingPopup') %>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const serverSettingsForm = document.getElementById('serverSettingsForm');
    const restartButton = document.getElementById('restartButton');
    const reinstallButton = document.getElementById('reinstallButton');

    serverSettingsForm.addEventListener('submit', function(e) {
      e.preventDefault();

      const formData = new FormData(serverSettingsForm);
      const settings = {
        name: formData.get('name'),
        description: formData.get('description')
      };

      const loader = showLoadingPopup('Updating Server Settings', 'Saving your changes...');

      fetch(`/server/<%= server.UUID %>/settings`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings),
      })
      .then(response => response.json())
      .then(data => {
        loader.close();
        if (data.success) {
          showToast('Server settings updated successfully!', 'success');
          setTimeout(() => window.location.reload(), 1500);
        } else {
          showToast('Failed to update server settings: ' + (data.error || 'Unknown error'), 'error');
        }
      })
      .catch(error => {
        loader.close();
        console.error('Error:', error);
        showToast('An error occurred while updating the server settings.', 'error');
      });
    });

    // Restart confirmation modal
    const createRestartModal = () => {
      const modal = document.createElement('div');
      modal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center opacity-0 pointer-events-none transition-opacity duration-300';
      modal.id = 'restartModal';

      modal.innerHTML = `
        <div class="bg-white rounded-xl p-8 max-w-md w-full transform scale-95 transition-transform duration-300">
          <h2 class="text-2xl font-medium mb-1 text-neutral-800">Confirm Restart</h2>
          <p class="mb-6 text-neutral-600">Are you sure you want to restart the server? This will disconnect all users.</p>
          <div class="flex justify-end space-x-4">
            <button id="cancelRestartBtn" class="px-5 py-2 bg-neutral-200 text-neutral-800 rounded-xl hover:bg-neutral-300 transition">Cancel</button>
            <button id="confirmRestartBtn" class="w-full md:w-auto rounded-xl bg-red-600 hover:bg-red-500 text-white px-3 py-2 text-sm font-medium shadow-md transition focus:outline focus:outline-2 focus:outline-offset-2">Restart</button>
          </div>
        </div>
      `;

      document.body.appendChild(modal);

      setTimeout(() => {
        modal.classList.remove('opacity-0', 'pointer-events-none');
        modal.querySelector('div').classList.remove('scale-95');
        modal.querySelector('div').classList.add('scale-100');
      }, 10);

      document.getElementById('cancelRestartBtn').addEventListener('click', () => {
        modal.classList.add('opacity-0', 'pointer-events-none');
        modal.querySelector('div').classList.remove('scale-100');
        modal.querySelector('div').classList.add('scale-95');
        setTimeout(() => document.body.removeChild(modal), 300);
      });

      document.getElementById('confirmRestartBtn').addEventListener('click', () => {
        modal.classList.add('opacity-0', 'pointer-events-none');
        modal.querySelector('div').classList.remove('scale-100');
        modal.querySelector('div').classList.add('scale-95');
        setTimeout(() => document.body.removeChild(modal), 300);

        const loader = showLoadingPopup('Restarting Server', 'Initiating server restart...');

        fetch(`/server/<%= server.UUID %>/power/restart`, {
          method: 'POST',
        })
        .then(response => response.json())
        .then(data => {
          loader.close();
          if (data.success || data.message) {
            showToast('Server restart initiated.', 'success');
          } else {
            showToast('Failed to restart server: ' + (data.error || 'Unknown error'), 'error');
          }
        })
        .catch(error => {
          loader.close();
          console.error('Error:', error);
          showToast('An error occurred while restarting the server.', 'error');
        });
      });
    };

    restartButton.addEventListener('click', createRestartModal);

    // Reinstall confirmation modal
    const createReinstallModal = () => {
      const modal = document.createElement('div');
      modal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center opacity-0 pointer-events-none transition-opacity duration-300';
      modal.id = 'reinstallModal';

      modal.innerHTML = `
        <div class="bg-white rounded-xl p-8 max-w-md w-full transform scale-95 transition-transform duration-300">
          <h2 class="text-2xl font-medium mb-1 text-neutral-800">Confirm Reinstallation</h2>
          <p class="mb-2 text-neutral-600">WARNING: Are you sure you want to reinstall the server?</p>
          <p class="mb-6 text-red-500 font-medium">This will DELETE ALL DATA and reinstall the server from scratch. This action cannot be undone.</p>
          <div class="flex justify-end space-x-4">
            <button id="cancelReinstallBtn" class="px-5 py-2 bg-neutral-200 text-neutral-800 rounded-xl hover:bg-neutral-300 transition">Cancel</button>
            <button id="confirmReinstallBtn" class="w-full md:w-auto rounded-xl bg-red-600 hover:bg-red-500 text-white px-3 py-2 text-sm font-medium shadow-md transition focus:outline focus:outline-2 focus:outline-offset-2">Reinstall</button>
          </div>
        </div>
      `;

      document.body.appendChild(modal);

      setTimeout(() => {
        modal.classList.remove('opacity-0', 'pointer-events-none');
        modal.querySelector('div').classList.remove('scale-95');
        modal.querySelector('div').classList.add('scale-100');
      }, 10);

      document.getElementById('cancelReinstallBtn').addEventListener('click', () => {
        modal.classList.add('opacity-0', 'pointer-events-none');
        modal.querySelector('div').classList.remove('scale-100');
        modal.querySelector('div').classList.add('scale-95');
        setTimeout(() => document.body.removeChild(modal), 300);
      });

      document.getElementById('confirmReinstallBtn').addEventListener('click', () => {
        // Create final warning modal
        const finalWarningModal = document.createElement('div');
        finalWarningModal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center opacity-0 pointer-events-none transition-opacity duration-300';
        finalWarningModal.id = 'finalWarningModal';

        finalWarningModal.innerHTML = `
          <div class="bg-white rounded-xl p-8 max-w-md w-full transform scale-95 transition-transform duration-300">
            <h2 class="text-2xl font-medium mb-1 text-neutral-800">FINAL WARNING</h2>
            <p class="mb-6 text-red-500 font-medium">All server data will be permanently deleted. Continue with reinstallation?</p>
            <div class="flex justify-end space-x-4">
              <button id="cancelFinalWarningBtn" class="px-5 py-2 bg-neutral-200 text-neutral-800 rounded-xl hover:bg-neutral-300 transition">Cancel</button>
              <button id="confirmFinalWarningBtn" class="w-full md:w-auto rounded-xl bg-red-600 hover:bg-red-500 text-white px-3 py-2 text-sm font-medium shadow-md transition focus:outline focus:outline-2 focus:outline-offset-2">Proceed</button>
            </div>
          </div>
        `;

        // Close first modal
        modal.classList.add('opacity-0', 'pointer-events-none');
        modal.querySelector('div').classList.remove('scale-100');
        modal.querySelector('div').classList.add('scale-95');
        setTimeout(() => document.body.removeChild(modal), 300);

        // Show final warning modal
        document.body.appendChild(finalWarningModal);
        setTimeout(() => {
          finalWarningModal.classList.remove('opacity-0', 'pointer-events-none');
          finalWarningModal.querySelector('div').classList.remove('scale-95');
          finalWarningModal.querySelector('div').classList.add('scale-100');
        }, 10);

        document.getElementById('cancelFinalWarningBtn').addEventListener('click', () => {
          finalWarningModal.classList.add('opacity-0', 'pointer-events-none');
          finalWarningModal.querySelector('div').classList.remove('scale-100');
          finalWarningModal.querySelector('div').classList.add('scale-95');
          setTimeout(() => document.body.removeChild(finalWarningModal), 300);
        });

        document.getElementById('confirmFinalWarningBtn').addEventListener('click', () => {
          finalWarningModal.classList.add('opacity-0', 'pointer-events-none');
          finalWarningModal.querySelector('div').classList.remove('scale-100');
          finalWarningModal.querySelector('div').classList.add('scale-95');
          setTimeout(() => document.body.removeChild(finalWarningModal), 300);

          const loader = showLoadingPopup('Reinstalling Server', 'Initiating server reinstallation...');
          loader.updateProgress(10, 'Preparing for reinstallation...');

          fetch(`/server/<%= server.UUID %>/reinstall`, {
            method: 'POST',
          })
          .then(response => response.json())
          .then(data => {
            loader.updateProgress(100, 'Reinstallation initiated!');
            setTimeout(() => {
              loader.close();
              if (data.success) {
                showToast('Server reinstallation initiated. This may take a few minutes to complete.', 'success');
              } else {
                showToast('Failed to reinstall server: ' + (data.error || 'Unknown error'), 'error');
              }
            }, 1000);
          })
          .catch(error => {
            loader.close();
            console.error('Error:', error);
            showToast('An error occurred while reinstalling the server.', 'error');
          });
        });
      });
    };

    reinstallButton.addEventListener('click', createReinstallModal);
  });
</script>
