<%- include('../../components/header', { title: 'Worlds' }) %>

<main class="h-screen m-auto">
  <div class="flex h-screen">
    <!-- Sidebar -->
    <aside class="w-60 h-full">
      <%- include('../../components/template') %>
    </aside>

    <!-- Main Content -->
    <section class="flex-1 p-6 overflow-y-auto pt-16">
      <!-- <PERSON> Header -->
      <header class="sm:flex sm:items-center px-8 pt-4">
        <%- include('../../components/serverHeader') %>
      </header>

      <%- include('../../components/installHeader') %>

      <!-- Daemon down message removed as requested -->

      <!-- Server Template -->
      <%- include('../../components/serverTemplate') %>

      <div class="p-6">
        <!-- Error message -->
        <% if (errorMessage && errorMessage.message) { %>
          <div class="rounded-xl bg-red-800/10 px-6 py-4 mt-4 mx-8 mb-4">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-400">Error</h3>
                <p class="text-sm text-red-400/80 mt-1">
                  <%= errorMessage.message %>
                </p>
              </div>
            </div>
          </div>
        <% } %>

        <!-- No worlds message -->
        <div id="noWorldsMessage" class="<%= worlds.length > 0 ? 'hidden' : '' %> rounded-xl bg-blue-800/10 px-6 py-4 mt-8 mx-8">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-blue-400">No worlds found</h3>
              <p class="text-sm text-blue-400/80 mt-1">
                No Minecraft worlds were detected on this server. Worlds will appear here once they are created.
              </p>
            </div>
          </div>
        </div>

        <!-- Table of worlds -->
        <div class="overflow-hidden shadow rounded-lg m-8 border border-neutral-800/20 bg-white dark:bg-neutral-800 <%= worlds.length === 0 ? 'hidden' : '' %>" id="serverTable">
          <table class="min-w-full divide-y divide-neutral-200 dark:divide-neutral-700">
            <thead class="bg-neutral-50 dark:bg-neutral-800">
              <tr>
                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-medium text-neutral-800 dark:text-white sm:pl-6"><%= req.translations.name %></th>
                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-medium text-neutral-800 dark:text-white sm:pl-6"><%= req.translations.actions %></th>
              </tr>
            </thead>
            <tbody class="divide-y divide-neutral-200 dark:divide-neutral-700 bg-white dark:bg-neutral-800">
              <% worlds.forEach(function(world) { %>
                <tr class="hover:bg-neutral-50 dark:hover:bg-neutral-700 transition-colors">
                  <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm sm:pl-6">
                    <div class="flex items-center">
                      <div class="mr-5 flex-shrink-0">
                        <%
                          let iconPath = '';
                          if (world.name === 'world') {
                            iconPath = '/assets/world_icons/overworld.png';
                          } else if (world.name === 'world_nether') {
                            iconPath = '/assets/world_icons/nether.png';
                          } else if (world.name === 'world_the_end') {
                            iconPath = '/assets/world_icons/end.png';
                          } else if (world.name.includes('nether')) {
                            iconPath = '/assets/world_icons/nether.png';
                          } else if (world.name.includes('end')) {
                            iconPath = '/assets/world_icons/end.png';
                          } else {
                            iconPath = '/assets/world_icons/overworld.png';
                          }
                        %>
                        <img class="h-10 w-10 rounded-full border border-neutral-200 dark:border-neutral-600" src="<%= iconPath %>" alt="World icon" />
                      </div>
                      <div>
                        <div class="font-medium text-neutral-800 dark:text-white"><%= world.name %></div>
                        <div class="text-neutral-500 dark:text-neutral-400 text-xs mt-1">Minecraft World</div>
                      </div>
                    </div>
                  </td>
                  <td class="whitespace-nowrap px-3 py-4 text-sm">
                    <div class="flex gap-3">
                      <button
                        type="button"
                        class="rounded-xl bg-red-600 px-3 py-2 text-center text-sm font-medium text-white shadow-sm hover:bg-red-500 transition-colors"
                        onclick="confirmDeleteWorld('<%= world.name %>')"
                      >
                        <div class="flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                          Remove
                        </div>
                      </button>
                    </div>
                  </td>
                </tr>
              <% }); %>
            </tbody>
          </table>
        </div>

      </div>
    </section>
  </div>
</main>

<!-- Delete World Confirmation Modal -->
<div id="deleteWorldModal" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 opacity-0 pointer-events-none transition-opacity duration-300">
  <div class="bg-white dark:bg-neutral-800 rounded-xl p-6 max-w-md w-full transform scale-95 transition-transform duration-300 shadow-xl">
    <div class="flex items-center mb-4">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
        <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
      </svg>
      <h3 class="text-lg font-medium text-neutral-900 dark:text-white">Delete World</h3>
    </div>
    <p class="text-neutral-600 dark:text-neutral-400 mb-6">
      Are you sure you want to delete the world <span id="worldNameToDelete" class="font-semibold text-neutral-900 dark:text-white"></span>?
      <br><br>
      <span class="text-red-500 font-medium">Warning:</span> This action cannot be undone and all world data will be permanently lost.
    </p>
    <div class="flex justify-end gap-3">
      <button id="cancelDeleteBtn" class="px-4 py-2 rounded-lg bg-neutral-200 text-neutral-800 hover:bg-neutral-300 dark:bg-neutral-700 dark:text-white dark:hover:bg-neutral-600 transition-colors">
        Cancel
      </button>
      <button id="confirmDeleteBtn" class="px-4 py-2 rounded-lg bg-red-600 text-white hover:bg-red-500 transition-colors flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
          <path stroke-linecap="round" stroke-linejoin="round" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
        </svg>
        Delete World
      </button>
    </div>
  </div>
</div>

<%- include('../../components/toast') %>
<%- include('../../components/footer') %>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Hide loading indicator when worlds are loaded
    const loadingIndicator = document.getElementById('loadingIndicator');
    const noWorldsMessage = document.getElementById('noWorldsMessage');
    const serverTable = document.getElementById('serverTable');

    // Hide loading indicator after a short delay to prevent flickering
    setTimeout(() => {
      loadingIndicator.style.display = 'none';

      // Show appropriate content based on worlds availability
      if (<%= worlds.length %> > 0) {
        serverTable.style.display = 'block';
        noWorldsMessage.style.display = 'none';
      } else {
        serverTable.style.display = 'none';
        noWorldsMessage.style.display = 'block';
      }
    }, 500);

    // WebSocket for server status
    const wsCheck = new WebSocket(`${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/ws/server/<%= server.UUID %>/status`);

    wsCheck.onopen = function() {
      // Connection established
    };

    wsCheck.onmessage = function(event) {
      // No need to show daemon down message as it's been removed
      console.log('WebSocket message received:', JSON.parse(event.data));
    };

    wsCheck.onerror = function() {
      // No need to show daemon down message as it's been removed
      console.error('WebSocket connection error');
    };

    // Close WebSocket when page is unloaded
    window.addEventListener('beforeunload', function() {
      if (wsCheck && wsCheck.readyState === WebSocket.OPEN) {
        wsCheck.close();
      }
    });
  });

  // Delete world confirmation modal
  const deleteWorldModal = document.getElementById('deleteWorldModal');
  const worldNameToDelete = document.getElementById('worldNameToDelete');
  const cancelDeleteBtn = document.getElementById('cancelDeleteBtn');
  const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
  let currentWorldToDelete = '';

  function confirmDeleteWorld(worldName) {
    currentWorldToDelete = worldName;
    worldNameToDelete.textContent = worldName;

    // Show modal
    deleteWorldModal.classList.remove('opacity-0', 'pointer-events-none');
    deleteWorldModal.querySelector('div').classList.remove('scale-95');
    deleteWorldModal.querySelector('div').classList.add('scale-100');
  }

  function closeDeleteModal() {
    deleteWorldModal.classList.add('opacity-0', 'pointer-events-none');
    deleteWorldModal.querySelector('div').classList.remove('scale-100');
    deleteWorldModal.querySelector('div').classList.add('scale-95');
  }

  // Close modal when clicking outside or pressing Escape
  deleteWorldModal.addEventListener('click', function(event) {
    if (event.target === deleteWorldModal) {
      closeDeleteModal();
    }
  });

  document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape' && !deleteWorldModal.classList.contains('opacity-0')) {
      closeDeleteModal();
    }
  });

  cancelDeleteBtn.addEventListener('click', closeDeleteModal);

  confirmDeleteBtn.addEventListener('click', function() {
    // Disable button and show loading state
    this.disabled = true;
    const originalContent = this.innerHTML;
    this.innerHTML = `
      <svg class="animate-spin h-4 w-4 mr-1.5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      Deleting...
    `;

    closeDeleteModal();
    deleteWorld(currentWorldToDelete, () => {
      // Reset button state if needed
      this.disabled = false;
      this.innerHTML = originalContent;
    });
  });

  function deleteWorld(worldName, callback) {
    // Show loading toast
    showToast(`Deleting world ${worldName}...`, 'info');

    fetch('/server/<%= server.UUID %>/files/rm/' + encodeURIComponent(worldName), {
      method: 'DELETE'
    })
    .then(response => {
      if (!response.ok) {
        return response.json().then(data => {
          throw new Error(data.error || 'Failed to delete world');
        });
      }
      return response.json();
    })
    .then(data => {
      if (data.success) {
        showToast(`World ${worldName} deleted successfully`, 'success');

        // Fade out the deleted row
        const rows = document.querySelectorAll('tr');
        for (const row of rows) {
          if (row.textContent.includes(worldName)) {
            row.style.transition = 'opacity 0.5s';
            row.style.opacity = '0';
            break;
          }
        }

        // Reload after animation
        setTimeout(() => location.reload(), 800);
      } else {
        showToast(data.error || 'Failed to delete world', 'error');
        if (callback) callback();
      }
    })
    .catch(error => {
      console.error('Error deleting world:', error);
      showToast(error.message || 'Failed to delete world', 'error');
      if (callback) callback();
    });
  }
</script>